import React from 'react'
import { motion } from 'motion/react'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'

interface LampBackgroundProps {
  children: React.ReactNode
  className?: string
}

const LampBackground: React.FC<LampBackgroundProps> = ({ children, className }) => {
  const { isDark } = useThemeContext()

  return (
    <div
      className={cn(
        'relative flex min-h-screen flex-col overflow-hidden w-full z-0',
        isDark ? 'bg-slate-950' : 'bg-background',
        className
      )}
    >
      {/* Modified Aceternity Lamp positioned at top */}
      <div className="relative flex w-full items-start justify-center isolate z-0 pt-8">
        <motion.div
          initial={{ opacity: 0.5, width: '15rem' }}
          whileInView={{ opacity: 1, width: '30rem' }}
          transition={{
            delay: 0.3,
            duration: 0.8,
            ease: 'easeInOut'
          }}
          style={{
            backgroundImage: `conic-gradient(var(--conic-position), var(--tw-gradient-stops))`
          }}
          className={cn(
            "absolute inset-auto right-1/2 h-56 overflow-visible w-[30rem] bg-gradient-conic text-white [--conic-position:from_70deg_at_center_top]",
            isDark
              ? "from-cyan-500 via-transparent to-transparent"
              : "from-blue-400 via-transparent to-transparent"
          )}
        >
          <div className={cn(
            "absolute w-[100%] left-0 h-40 bottom-0 z-20 [mask-image:linear-gradient(to_top,white,transparent)]",
            isDark ? "bg-slate-950" : "bg-background"
          )} />
          <div className={cn(
            "absolute w-40 h-[100%] left-0 bottom-0 z-20 [mask-image:linear-gradient(to_right,white,transparent)]",
            isDark ? "bg-slate-950" : "bg-background"
          )} />
        </motion.div>

        <motion.div
          initial={{ opacity: 0.5, width: '15rem' }}
          whileInView={{ opacity: 1, width: '30rem' }}
          transition={{
            delay: 0.3,
            duration: 0.8,
            ease: 'easeInOut'
          }}
          style={{
            backgroundImage: `conic-gradient(var(--conic-position), var(--tw-gradient-stops))`
          }}
          className={cn(
            "absolute inset-auto left-1/2 h-56 w-[30rem] bg-gradient-conic text-white [--conic-position:from_290deg_at_center_top]",
            isDark
              ? "from-transparent via-transparent to-cyan-500"
              : "from-transparent via-transparent to-blue-400"
          )}
        >
          <div className={cn(
            "absolute w-40 h-[100%] right-0 bottom-0 z-20 [mask-image:linear-gradient(to_left,white,transparent)]",
            isDark ? "bg-slate-950" : "bg-background"
          )} />
          <div className={cn(
            "absolute w-[100%] right-0 h-40 bottom-0 z-20 [mask-image:linear-gradient(to_top,white,transparent)]",
            isDark ? "bg-slate-950" : "bg-background"
          )} />
        </motion.div>

        <div className={cn(
          "absolute top-32 h-48 w-full scale-x-150 blur-2xl",
          isDark ? "bg-slate-950" : "bg-background"
        )}></div>

        <div className="absolute top-32 z-50 h-48 w-full bg-transparent opacity-10 backdrop-blur-md"></div>

        <div className={cn(
          "absolute inset-auto z-50 h-36 w-[28rem] top-16 rounded-full opacity-50 blur-3xl",
          isDark ? "bg-cyan-500" : "bg-blue-400"
        )}></div>

        <motion.div
          initial={{ width: '8rem' }}
          whileInView={{ width: '16rem' }}
          transition={{
            delay: 0.3,
            duration: 0.8,
            ease: 'easeInOut'
          }}
          className={cn(
            "absolute inset-auto z-30 h-36 w-64 top-8 rounded-full blur-2xl",
            isDark ? "bg-cyan-400" : "bg-blue-300"
          )}
        ></motion.div>

        <motion.div
          initial={{ width: '15rem' }}
          whileInView={{ width: '30rem' }}
          transition={{
            delay: 0.3,
            duration: 0.8,
            ease: 'easeInOut'
          }}
          className={cn(
            "absolute inset-auto z-50 h-0.5 w-[30rem] top-4",
            isDark ? "bg-cyan-400" : "bg-blue-400"
          )}
        ></motion.div>

        <div className={cn(
          "absolute inset-auto z-40 h-44 w-full top-0",
          isDark ? "bg-slate-950" : "bg-background"
        )}></div>
      </div>

      {/* Content with glassmorphic effect */}
      <div className="relative z-50 flex flex-col px-5 pt-20">
        {children}
      </div>
    </div>
  )
}

export default LampBackground
