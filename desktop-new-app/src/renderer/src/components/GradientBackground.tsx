import React from 'react'
import { useThemeContext } from '../context/useThemeContext'

interface GradientBackgroundProps {
  children: React.ReactNode
}

const GradientBackground: React.FC<GradientBackgroundProps> = ({ children }) => {
  const { isDark } = useThemeContext()

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated background beams for auth pages */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Base gradient */}
        <div
          className={`absolute inset-0 ${
            isDark
              ? 'bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900'
              : 'bg-gradient-to-br from-blue-50 via-indigo-100 to-purple-50'
          }`}
        />

        {/* Animated beams */}
        <div className="absolute inset-0">
          {/* Beam 1 */}
          <div
            className={`absolute top-0 left-1/4 w-px h-full ${
              isDark ? 'bg-gradient-to-b from-blue-500/50 via-purple-500/30 to-transparent'
                     : 'bg-gradient-to-b from-blue-400/40 via-purple-400/20 to-transparent'
            } animate-pulse`}
            style={{ animationDelay: '0s', animationDuration: '3s' }}
          />

          {/* Beam 2 */}
          <div
            className={`absolute top-0 right-1/3 w-px h-full ${
              isDark ? 'bg-gradient-to-b from-cyan-500/50 via-blue-500/30 to-transparent'
                     : 'bg-gradient-to-b from-cyan-400/40 via-blue-400/20 to-transparent'
            } animate-pulse`}
            style={{ animationDelay: '1s', animationDuration: '4s' }}
          />

          {/* Beam 3 */}
          <div
            className={`absolute top-0 left-2/3 w-px h-full ${
              isDark ? 'bg-gradient-to-b from-purple-500/50 via-pink-500/30 to-transparent'
                     : 'bg-gradient-to-b from-purple-400/40 via-pink-400/20 to-transparent'
            } animate-pulse`}
            style={{ animationDelay: '2s', animationDuration: '5s' }}
          />

          {/* Horizontal beams */}
          <div
            className={`absolute top-1/4 left-0 w-full h-px ${
              isDark ? 'bg-gradient-to-r from-transparent via-blue-500/30 to-transparent'
                     : 'bg-gradient-to-r from-transparent via-blue-400/20 to-transparent'
            } animate-pulse`}
            style={{ animationDelay: '1.5s', animationDuration: '6s' }}
          />

          <div
            className={`absolute bottom-1/3 left-0 w-full h-px ${
              isDark ? 'bg-gradient-to-r from-transparent via-purple-500/30 to-transparent'
                     : 'bg-gradient-to-r from-transparent via-purple-400/20 to-transparent'
            } animate-pulse`}
            style={{ animationDelay: '3s', animationDuration: '4s' }}
          />
        </div>

        {/* Floating orbs */}
        <div
          className={`absolute top-1/4 left-1/4 w-32 h-32 ${
            isDark ? 'bg-blue-500/20' : 'bg-blue-400/15'
          } rounded-full blur-xl animate-pulse`}
          style={{ animationDelay: '0.5s', animationDuration: '8s' }}
        />
        <div
          className={`absolute bottom-1/4 right-1/4 w-24 h-24 ${
            isDark ? 'bg-purple-500/20' : 'bg-purple-400/15'
          } rounded-full blur-xl animate-pulse`}
          style={{ animationDelay: '2.5s', animationDuration: '7s' }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default GradientBackground
