import React, { useMemo, useState, useCallback, useEffect, ReactNode } from 'react'
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  flexRender,
  createColumnHelper,
  SortingState,
  getPaginationRowModel,
  PaginationState,
  VisibilityState,
  ColumnDef // Import ColumnDef
} from '@tanstack/react-table'
import {
  FiChevronLeft,
  FiChevronRight,
  FiEdit2,
  FiTrash2,
  FiCheck,
  FiX,
  FiDownload
} from 'react-icons/fi'
import { TransactionData } from '../../../shared/api'
import { Card } from './ui/Card'
import { Button } from './ui/Button'
import { Input } from './ui/Input'

// Define the row type
export interface CSVRow {
  id: string
  [key: string]: string | number | boolean | object | null | undefined
}

// Define the metadata for row operations
export interface RowOperationMetadata {
  action: 'update' | 'delete'
  rowId: string
  updatedRow?: CSVRow
  deletedRow?: CSVRow
  updatedRows: CSVRow[]
  headers: string[]
  isSingleRowOperation?: boolean
  complaintMetadata?: unknown
}

interface CsvDataTableProps {
  data: TransactionData[]
  loading: boolean
  onSave?: (data: string, metadata?: RowOperationMetadata) => Promise<unknown> | void
  onDownload?: () => void
  hiddenColumns?: string[]
  onTotalsCalculated?: (totalHold: number, totalWithdrawal: number) => void
  complaintMetadata?: unknown
}

const CsvDataTable: React.FC<CsvDataTableProps> = ({
  data: initialData,
  loading: externalLoading = false,
  onSave,
  onDownload,
  hiddenColumns = [],
  onTotalsCalculated,
  complaintMetadata
}) => {
  const [rows, setRows] = useState<CSVRow[]>([])
  const [headers, setHeaders] = useState<string[]>([])
  const [sorting, setSorting] = useState<SortingState>([])
  const [{ pageIndex, pageSize }, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 50
  })
  const [editableRow, setEditableRow] = useState<string | null>(null)
  const [editedValues, setEditedValues] = useState<Record<string, unknown>>({})
  const [editingCell, setEditingCell] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [minAmount, setMinAmount] = useState<number>(0)
  const [maxAmount, setMaxAmount] = useState<number>(0)
  const [amountFilterType, setAmountFilterType] = useState<'none' | 'range'>('none')

  const isLoading = externalLoading

  const rowsToCSV = useCallback((rowsToConvert: CSVRow[], headersToUse: string[]): string => {
    let csv = headersToUse.join(',') + '\n'
    rowsToConvert.forEach((row) => {
      const rowValues = headersToUse.map((header) => {
        const value = row[header]
        if (value && String(value).includes(',')) {
          return `"${value}"`
        }
        return value || ''
      })
      csv += rowValues.join(',') + '\n'
    })
    return csv
  }, [])

  useEffect(() => {
    if (!initialData || initialData.length === 0) {
      setRows([])
      setHeaders([])
      return
    }

    // Convert TransactionData[] to CSVRow[] and generate headers
    const generatedHeaders = Object.keys(initialData[0]).filter((key) => key !== 'id') // Exclude 'id'
    const generatedRows = initialData.map((item, index) => {
      const row: CSVRow = { id: `row-${index}` }
      generatedHeaders.forEach((header) => {
        row[header] = item[header as keyof TransactionData]
      })
      return row
    })

    setHeaders(generatedHeaders)
    setRows(generatedRows)
  }, [initialData])

  useEffect(() => {
    if (headers.length > 0 && hiddenColumns.length > 0) {
      const newVisibility: VisibilityState = {}
      headers.forEach((header) => {
        newVisibility[header] = !hiddenColumns.includes(header)
      })
      setColumnVisibility(newVisibility)
    }
  }, [headers, hiddenColumns])

  const parseAmount = (amountStr: string): number => {
    if (!amountStr) return 0
    const cleanAmount = String(amountStr)
      .replace(/[₹,\s]/g, '')
      .trim()
    return parseFloat(cleanAmount) || 0
  }

  const filteredRows = useMemo(() => {
    let filtered = rows

    if (searchTerm) {
      const searchTermLower = searchTerm.toLowerCase()
      const searchableColumns = [
        'Txn ID',
        'Sender Account',
        'Receiver Account',
        'Amount',
        'Txn Type'
      ]

      filtered = filtered.filter((row) => {
        for (const col of searchableColumns) {
          if (row[col] && String(row[col]).toLowerCase().includes(searchTermLower)) {
            return true
          }
        }
        return Object.entries(row).some(([key, value]) => {
          if (key === 'id' || searchableColumns.includes(key)) return false
          return String(value).toLowerCase().includes(searchTermLower)
        })
      })
    }

    if (amountFilterType === 'range' && (minAmount > 0 || maxAmount > 0)) {
      filtered = filtered.filter((row) => {
        const amount = parseAmount(String(row['Amount'] || '0'))
        if (minAmount > 0 && amount < minAmount) {
          return false
        }
        if (maxAmount > 0 && amount > maxAmount) {
          return false
        }
        return true
      })
    }
    return filtered
  }, [rows, searchTerm, amountFilterType, minAmount, maxAmount])

  const { totalHold, totalWithdrawal } = useMemo(() => {
    return rows.reduce(
      (totals, row) => {
        const txnType = String(row['Txn Type'] || '').toLowerCase()
        const amountStr = String(row['Amount'] || '0')
        const amount = parseFloat(amountStr.replace(/[^\d.]/g, '')) || 0

        if (txnType.includes('hold')) {
          totals.totalHold += amount
        } else if (txnType.includes('withdrawal') || txnType.includes('atm')) {
          totals.totalWithdrawal += amount
        }
        return totals
      },
      { totalHold: 0, totalWithdrawal: 0 }
    )
  }, [rows])

  useEffect(() => {
    if (onTotalsCalculated) {
      onTotalsCalculated(totalHold, totalWithdrawal)
    }
  }, [totalHold, totalWithdrawal, onTotalsCalculated])

  const handleEditRow = useCallback(
    (rowId: string) => {
      const rowToEdit = rows.find((row) => row.id === rowId)
      if (!rowToEdit) {
        return
      }
      setEditableRow(rowId)
      setEditedValues({})
    },
    [rows]
  )

  const handleCancelEdit = useCallback(() => {
    setEditableRow(null)
    setEditedValues({})
    setEditingCell(null)
  }, [])

  const handleSaveRow = useCallback(
    (rowId: string) => {
      const rowIndex = rows.findIndex((row) => row.id === rowId)
      if (rowIndex === -1) {
        return
      }
      const updatedRows = [...rows]
      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        ...(editedValues as Partial<CSVRow>)
      }
      const originalRows = [...rows] // Keep originalRows for potential rollback
      setRows(updatedRows) // Optimistic update
      setEditableRow(null)
      setEditedValues({})
      setEditingCell(null)
      const updatedCSV = rowsToCSV(updatedRows, headers)

      if (onSave) {
        const metadata: RowOperationMetadata = {
          action: 'update',
          rowId,
          updatedRow: updatedRows[rowIndex],
          updatedRows,
          headers,
          isSingleRowOperation: true,
          complaintMetadata
        }
        // onSave is expected to handle its own loading state
        const saveResult = onSave(updatedCSV, metadata)
        if (saveResult && typeof (saveResult as Promise<unknown>).then === 'function') {
          ;(saveResult as Promise<unknown>).catch(() => {
            setRows(originalRows) // Rollback on error
          })
        }
      }
    },
    [rows, headers, editedValues, rowsToCSV, onSave, complaintMetadata]
  )

  const handleDeleteRow = useCallback(
    (rowId: string) => {
      const rowIndex = rows.findIndex((row) => row.id === rowId)
      if (rowIndex === -1) {
        return
      }
      const updatedRows = [...rows]
      const deletedRow = updatedRows[rowIndex]
      updatedRows.splice(rowIndex, 1)
      const originalRows = [...rows] // Keep originalRows for potential rollback
      setRows(updatedRows) // Optimistic update
      const updatedCSV = rowsToCSV(updatedRows, headers)

      if (onSave) {
        const metadata: RowOperationMetadata = {
          action: 'delete',
          rowId,
          deletedRow,
          updatedRows,
          headers,
          isSingleRowOperation: true,
          complaintMetadata
        }
        // onSave is expected to handle its own loading state
        const saveResult = onSave(updatedCSV, metadata)
        if (saveResult && typeof (saveResult as Promise<unknown>).then === 'function') {
          ;(saveResult as Promise<unknown>).catch(() => {
            setRows(originalRows) // Rollback on error
          })
        }
      }
    },
    [rows, headers, rowsToCSV, onSave, complaintMetadata]
  )

  const columnHelper = createColumnHelper<CSVRow>()

  const columns = useMemo((): ColumnDef<CSVRow>[] => {
    if (headers.length === 0) return []

    const txnTypeColorMap = {
      'money transfer to': 'bg-amber-500/20 text-amber-800 dark:text-amber-200',
      upi: 'bg-blue-500/20 text-blue-800 dark:text-blue-200',
      atm: 'bg-red-500/20 text-red-800 dark:text-red-200',
      withdrawal: 'bg-red-500/20 text-red-800 dark:text-red-200',
      hold: 'bg-green-500/20 text-green-800 dark:text-green-200',
      others: 'bg-gray-500/20 text-gray-800 dark:text-gray-200'
    }

    const baseColumns = headers
      .map((header) => {
        // Combine receiver_account and receiver_ifsc
        if (header === 'receiver_account') {
          return columnHelper.display({
            id: 'receiver_account_ifsc',
            header: 'Receiver Account / IFSC',
            cell: ({ row }) => {
              const phrasesToHide = [
                'Transaction ID / UTR Number-:',
                'Transaction Amount-:',
                'Disputed Amount:'
              ]

              const cleanAndDisplay = (value: unknown): ReactNode => {
                const strValue = String(value || '').trim()
                if (phrasesToHide.some((phrase) => strValue.includes(phrase))) {
                  return ''
                }
                return strValue
              }

              const account = cleanAndDisplay(row.original.receiver_account)
              const ifsc = cleanAndDisplay(row.original.receiver_ifsc)

              return (
                <>
                  {account && <div>Acc: {account}</div>}
                  {ifsc && <div>IFSC: {ifsc}</div>}
                  {!account && !ifsc && <div>N/A</div>}
                </>
              )
            }
          })
        }
        // Skip receiver_ifsc as it's combined
        if (header === 'receiver_ifsc') {
          return null
        }

        return columnHelper.accessor(header, {
          header: () => header,
          cell: ({ row, getValue }) => {
            const value = getValue() as string | number | boolean | object | null | undefined
            const isEditing = editableRow === row.original.id

            if (isEditing) {
              const cellId = `${row.original.id}-${header}`
              const isCurrentlyEditing = editingCell === cellId

              return (
                <input
                  type="text"
                  value={
                    editedValues[header] !== undefined
                      ? (editedValues[header] as string)
                      : (value as string)
                  }
                  onChange={(e) =>
                    setEditedValues({
                      ...editedValues,
                      [header]: e.target.value
                    })
                  }
                  onFocus={() => setEditingCell(cellId)}
                  autoFocus={isCurrentlyEditing}
                  className="w-full p-1 border border-gray-300 dark:border-gray-700 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              )
            }

            if (header === 'Txn Type' && value) {
              const valueLower = String(value).toLowerCase()
              let colorClass = ''

              console.log(`Txn Type: ${value}, Lowercase: ${valueLower}`) // Debugging color issue

              for (const [type, className] of Object.entries(txnTypeColorMap)) {
                if (valueLower.includes(type)) {
                  colorClass = className
                  break
                }
              }

              console.log(`Color Class for ${valueLower}: ${colorClass}`) // Debugging color issue

              if (colorClass) {
                return (
                  <span className={`px-2 py-1 rounded-full ${colorClass}`}>{String(value)}</span>
                )
              }
            }
            // Ensure all non-primitive values are stringified before rendering
            if (typeof value === 'object' && value !== null) {
              return <span>{JSON.stringify(value)}</span>
            }
            return <span>{String(value)}</span>
          }
        })
      })
      .filter((column): column is ColumnDef<CSVRow> => column !== null) // Filter out nulls and assert type

    const actionColumn = columnHelper.display({
      id: 'actions',
      header: () => 'Actions',
      cell: ({ row }) => {
        const isEditing = editableRow === row.original.id

        if (isEditing) {
          return (
            <div className="flex space-x-2">
              <button
                onClick={() => handleSaveRow(row.original.id)}
                className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                title="Save"
              >
                <FiCheck size={16} />
              </button>
              <button
                onClick={() => handleCancelEdit()}
                className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                title="Cancel"
              >
                <FiX size={16} />
              </button>
            </div>
          )
        }

        return (
          <div className="flex flex-row justify-center gap-2">
            <button
              onClick={() => handleEditRow(row.original.id)}
              className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              title="Edit"
              disabled={isLoading}
            >
              <FiEdit2 size={14} />
            </button>
            <button
              onClick={() => handleDeleteRow(row.original.id)}
              className="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
              title="Delete"
              disabled={isLoading}
            >
              <FiTrash2 size={14} />
            </button>
          </div>
        )
      }
    })

    // Filter out hidden columns
    const finalColumns = [...baseColumns, actionColumn].filter(
      (column) => !hiddenColumns.includes(column.id as string)
    )

    return finalColumns
  }, [
    headers,
    editableRow,
    editedValues,
    editingCell,
    isLoading,
    handleSaveRow,
    handleCancelEdit,
    handleEditRow,
    handleDeleteRow,
    columnHelper,
    hiddenColumns // Added hiddenColumns to dependencies
  ])

  const table = useReactTable({
    data: filteredRows,
    columns,
    state: {
      sorting,
      pagination: { pageIndex, pageSize },
      columnVisibility
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel()
  })

  return (
    <Card className="w-full h-full overflow-hidden backdrop-blur-md bg-white/70 dark:bg-white/5 border border-gray-200/50 dark:border-white/10">
      {isLoading && (
        <div className="absolute inset-0 bg-white/50 dark:bg-black/50 z-50 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">Processing data...</p>
          </div>
        </div>
      )}
      <div className="flex flex-col gap-4 p-4">
        {' '}
        {/* Replaced Card.Header */}
        {/* Search and Amount Filter Row */}
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <div className="flex items-center">
            <Input // Changed TextField to Input
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full max-w-md"
            />
          </div>

          {/* Amount Filter Controls */}
          <div className="flex items-center gap-3 flex-wrap">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Amount Filter:
            </span>

            {/* Radio buttons */}
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                <input
                  type="radio"
                  name="amountFilter"
                  checked={amountFilterType === 'none'}
                  onChange={() => setAmountFilterType('none')}
                  className="text-blue-600 focus:ring-blue-500"
                />
                None
              </label>

              <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                <input
                  type="radio"
                  name="amountFilter"
                  checked={amountFilterType === 'range'}
                  onChange={() => setAmountFilterType('range')}
                  className="text-blue-600 focus:ring-blue-500"
                />
                Range
              </label>
            </div>

            {amountFilterType === 'range' && (
              <>
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Min:
                  </label>
                  <input
                    type="number"
                    placeholder="0"
                    value={minAmount || ''}
                    onChange={(e) => setMinAmount(Number(e.target.value) || 0)}
                    className="w-24 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Max:
                  </label>
                  <input
                    type="number"
                    placeholder="∞"
                    value={maxAmount || ''}
                    onChange={(e) => setMaxAmount(Number(e.target.value) || 0)}
                    className="w-24 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    setMinAmount(0)
                    setMaxAmount(0)
                    setAmountFilterType('none')
                  }}
                  className="px-3 py-1 text-sm"
                >
                  Clear
                </Button>
              </>
            )}
          </div>
        </div>
        {/* Transaction Summary and Download */}
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 w-full">
          <div className="flex items-center gap-4">
            <div className="flex flex-col items-center px-6 py-2 rounded-md bg-green-100/30 dark:bg-green-900/30 border border-green-200 dark:border-green-800/50 min-w-[180px]">
              <span className="text-xs font-medium text-green-700 dark:text-green-400">
                Total Amount on Hold
              </span>
              <span className="text-sm font-bold text-green-800 dark:text-green-300">
                ₹{totalHold.toLocaleString('en-IN', { maximumFractionDigits: 2 })}
              </span>
            </div>
            <div className="flex flex-col items-center px-6 py-2 rounded-md bg-red-100/30 dark:bg-red-900/30 border border-red-200 dark:border-red-800/50 min-w-[180px]">
              <span className="text-xs font-medium text-red-700 dark:text-red-400">
                Total Withdrawal
              </span>
              <span className="text-sm font-bold text-red-800 dark:text-red-300">
                ₹{totalWithdrawal.toLocaleString('en-IN', { maximumFractionDigits: 2 })}
              </span>
            </div>
            {/* Show filtered count if filters are active */}
            {(searchTerm || amountFilterType === 'range') && (
              <div className="flex flex-col items-center px-4 py-2 rounded-md bg-blue-100/30 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800/50">
                <span className="text-xs font-medium text-blue-700 dark:text-blue-400">
                  Filtered Results
                </span>
                <span className="text-sm font-bold text-blue-800 dark:text-blue-300">
                  {filteredRows.length} of {rows.length}
                </span>
              </div>
            )}
          </div>

          <div className="flex gap-2">
            {onDownload && (
              <Button
                variant="default"
                size="sm"
                onClick={onDownload}
                disabled={isLoading || !initialData}
                className="px-2 py-1 text-xs font-medium"
              >
                <FiDownload className="mr-2" />
                Download Excel
              </Button>
            )}
          </div>
        </div>
      </div>
      {/* Closing div for Card.Header replacement */}
      <div className="p-0 border-0 overflow-auto">
        {' '}
        {/* Replaced Card.Content */}
        <div className="overflow-x-auto w-full">
          {headers.length > 0 ? (
            <>
              <style>
                {`
                      .csv-preview-table {
                        width: 100%;
                        table-layout: auto;
                        border-collapse: collapse;
                        border: 1px solid rgba(229, 231, 235, 0.5);
                        font-size: 0.875rem;
                        border-radius: 12px;
                        overflow: hidden;
                        backdrop-filter: blur(8px);
                        background: rgba(255, 255, 255, 0.7);
                      }
                      .dark .csv-preview-table {
                        border-color: rgba(255, 255, 255, 0.1);
                        background: rgba(255, 255, 255, 0.05);
                      }
                      .csv-preview-table th,
                      .csv-preview-table td {
                        padding: 12px;
                        border: 1px solid rgba(229, 231, 235, 0.3);
                        text-align: left;
                        vertical-align: middle;
                        line-height: 1.4;
                        box-sizing: border-box;
                        height: 48px;
                        position: relative;
                        word-break: break-word;
                        overflow-wrap: break-word;
                      }
                      .dark .csv-preview-table th,
                      .dark .csv-preview-table td {
                        border-color: rgba(255, 255, 255, 0.1);
                      }
                      .csv-preview-table th {
                        background: rgba(249, 250, 251, 0.7);
                        font-weight: 600;
                        color: #111827;
                        white-space: nowrap;
                        position: sticky;
                        top: 0;
                        z-index: 10;
                        backdrop-filter: blur(8px);
                      }
                      .dark .csv-preview-table th {
                        background: rgba(255, 255, 255, 0.05);
                        color: rgba(255, 255, 255, 0.9);
                      }
                      .csv-preview-table tbody tr {
                        transition: all 0.2s ease;
                      }
                      .csv-preview-table tbody tr:hover {
                        background: rgba(249, 250, 251, 0.7);
                        transform: scale(1.001);
                      }
                      .dark .csv-preview-table tbody tr:hover {
                        background: rgba(255, 255, 255, 0.05);
                      }
                      /* Remove specific column widths for Reference, Layer, Amount, Actions */
                      .csv-column-Reference,
                      .csv-column-Layer,
                      .csv-column-Amount,
                      .csv-column-actions {
                        width: auto !important;
                        min-width: 0;
                      }
                      @media (max-width: 768px) {
                        .csv-preview-table {
                          display: block;
                          overflow-x: auto;
                          white-space: nowrap;
                        }
                        .csv-preview-table thead,
                        .csv-preview-table tbody,
                        .csv-preview-table th,
                        .csv-preview-table td,
                        .csv-preview-table tr {
                          display: table-cell;
                        }
                        .csv-preview-table th {
                          display: table-cell;
                        }
                        .csv-preview-table tr {
                          display: table-row;
                        }
                      }
                    `}
              </style>
              <table className="csv-preview-table">
                <thead>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th key={header.id} className={`csv-column-${header.id}`}>
                          {flexRender(header.column.columnDef.header, header.getContext())}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map((row) => (
                      <tr key={row.id}>
                        {row.getVisibleCells().map((cell) => (
                          <td key={cell.id} className={`csv-column-${cell.column.id}`}>
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </td>
                        ))}
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={columns.length}
                        className="py-4 text-center text-gray-500 dark:text-gray-400"
                      >
                        No data available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </>
          ) : (
            <div className="flex justify-center items-center h-64">
              <p className="text-gray-500 dark:text-gray-400">No data available</p>
            </div>
          )}
        </div>
      </div>
      {/* Closing div for Card.Content replacement */}
      <div className="flex items-center justify-center mt-4 space-x-2">
        <button
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
          className="p-1"
        >
          <FiChevronLeft />
        </button>
        <span>
          Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
        </span>
        <button onClick={() => table.nextPage()} disabled={!table.getCanNextPage()} className="p-1">
          <FiChevronRight />
        </button>
      </div>
    </Card>
  )
}

export default CsvDataTable
