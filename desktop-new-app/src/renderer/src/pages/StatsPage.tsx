import React from 'react'
import { useParams } from 'react-router-dom'
import { useComplaintData } from '../hooks/useComplaintData'
import Stats from '../components/Stats'
import { TransactionData } from '../../../shared/api'

const StatsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const { data: complaint, loading, error } = useComplaintData(id || '')

  // Extract all transactions from layer_transactions
  const allTransactions: TransactionData[] = React.useMemo(() => {
    if (!complaint?.layer_transactions) return []
    
    return Object.values(complaint.layer_transactions)
      .flat()
      .filter((item): item is TransactionData => 
        typeof item === 'object' && 
        item !== null && 
        'receiver_ifsc' in item
      )
  }, [complaint])

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg">Loading complaint data...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-2">Error</div>
          <div className="text-gray-600">{error}</div>
        </div>
      </div>
    )
  }

  if (!complaint) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-600">Complaint not found</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto">
        <div className="mb-6 p-6 border-b">
          <h1 className="text-3xl font-bold">Geographic Fraud Analysis</h1>
          <p className="text-gray-600 mt-2">
            Complaint: {complaint.metadata?.complaint_number || complaint.title}
          </p>
          <p className="text-sm text-gray-500">
            Total Transactions: {allTransactions.length}
          </p>
        </div>
        
        <Stats transactions={allTransactions} loading={loading} />
      </div>
    </div>
  )
}

export default StatsPage
