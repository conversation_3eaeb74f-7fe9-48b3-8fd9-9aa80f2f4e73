import { validate, fetchDetails } from 'ifsc'

// IFSC Details interface
interface IFSCDetails {
  BANK: string
  IFSC: string
  BRANCH: string
  ADDRESS: string
  CONTACT: string
  CITY: string
  RTGS: boolean
  NEFT: boolean
  IMPS: boolean
  UPI: boolean
  DISTRICT: string
  STATE: string
  BANKCODE: string
  MICR?: string
}

/**
 * Main process IFSC service using Node.js packages
 */
export class MainIFSCService {
  private cache: Map<string, IFSCDetails | null> = new Map()
  private readonly maxCacheSize = 1000
  private readonly cacheExpiryMs = 24 * 60 * 60 * 1000 // 24 hours
  private cacheTimestamps: Map<string, number> = new Map()

  /**
   * Validate an IFSC code using the ifsc package
   */
  public validateIFSC(ifscCode: string): boolean {
    try {
      if (!ifscCode || typeof ifscCode !== 'string') {
        return false
      }
      
      // Clean the IFSC code
      const cleanIFSC = ifscCode.trim().toUpperCase()
      
      // Basic format validation (4 letters + 7 characters)
      if (!/^[A-Z]{4}[A-Z0-9]{7}$/.test(cleanIFSC)) {
        return false
      }
      
      // Use the ifsc package validation (Node.js only)
      return validate(cleanIFSC)
    } catch (error) {
      console.error('[Main IFSC Service] Error validating IFSC:', error)
      return false
    }
  }

  /**
   * Fetch IFSC details using the ifsc package
   */
  public async fetchIFSCDetails(ifscCode: string): Promise<IFSCDetails | null> {
    try {
      if (!ifscCode) return null
      
      const cleanIFSC = ifscCode.trim().toUpperCase()
      
      // Check cache first
      if (this.cache.has(cleanIFSC)) {
        const timestamp = this.cacheTimestamps.get(cleanIFSC)
        if (timestamp && Date.now() - timestamp < this.cacheExpiryMs) {
          console.log(`[Main IFSC Service] Cache hit for ${cleanIFSC}`)
          return this.cache.get(cleanIFSC) || null
        } else {
          // Cache expired, remove it
          this.cache.delete(cleanIFSC)
          this.cacheTimestamps.delete(cleanIFSC)
        }
      }
      
      // Validate before fetching
      if (!this.validateIFSC(cleanIFSC)) {
        console.log(`[Main IFSC Service] Invalid IFSC format: ${cleanIFSC}`)
        this.cacheResult(cleanIFSC, null)
        return null
      }
      
      console.log(`[Main IFSC Service] Fetching details for ${cleanIFSC}`)
      
      // Fetch from API using the ifsc package
      const details = await fetchDetails(cleanIFSC)
      
      // Cache the result
      this.cacheResult(cleanIFSC, details)
      
      console.log(`[Main IFSC Service] Successfully fetched details for ${cleanIFSC}`)
      return details
    } catch (error) {
      console.error(`[Main IFSC Service] Error fetching IFSC details for ${ifscCode}:`, error)
      // Cache null result to avoid repeated API calls for invalid codes
      this.cacheResult(ifscCode.trim().toUpperCase(), null)
      return null
    }
  }

  /**
   * Cache the result with timestamp
   */
  private cacheResult(ifscCode: string, result: IFSCDetails | null): void {
    try {
      // Implement LRU cache behavior
      if (this.cache.size >= this.maxCacheSize) {
        // Remove oldest entry
        const oldestKey = this.cache.keys().next().value
        if (oldestKey) {
          this.cache.delete(oldestKey)
          this.cacheTimestamps.delete(oldestKey)
        }
      }
      
      this.cache.set(ifscCode, result)
      this.cacheTimestamps.set(ifscCode, Date.now())
      
      console.log(`[Main IFSC Service] Cached result for ${ifscCode}, cache size: ${this.cache.size}`)
    } catch (error) {
      console.error('[Main IFSC Service] Error caching result:', error)
    }
  }

  /**
   * Clear the cache
   */
  public clearCache(): void {
    this.cache.clear()
    this.cacheTimestamps.clear()
    console.log('[Main IFSC Service] Cache cleared')
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): {
    size: number
    maxSize: number
    hitRate: number
  } {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: 0 // Could implement hit rate tracking if needed
    }
  }

  /**
   * Batch validate multiple IFSC codes
   */
  public validateMultipleIFSC(ifscCodes: string[]): Record<string, boolean> {
    const results: Record<string, boolean> = {}
    
    for (const ifscCode of ifscCodes) {
      if (ifscCode) {
        results[ifscCode] = this.validateIFSC(ifscCode)
      }
    }
    
    return results
  }

  /**
   * Batch fetch multiple IFSC details
   */
  public async fetchMultipleIFSCDetails(ifscCodes: string[]): Promise<Record<string, IFSCDetails | null>> {
    const results: Record<string, IFSCDetails | null> = {}
    
    // Process in parallel but with some rate limiting
    const batchSize = 5
    for (let i = 0; i < ifscCodes.length; i += batchSize) {
      const batch = ifscCodes.slice(i, i + batchSize)
      const batchPromises = batch.map(async (ifscCode) => {
        if (ifscCode) {
          const details = await this.fetchIFSCDetails(ifscCode)
          return { ifscCode, details }
        }
        return { ifscCode, details: null }
      })
      
      const batchResults = await Promise.all(batchPromises)
      batchResults.forEach(({ ifscCode, details }) => {
        results[ifscCode] = details
      })
      
      // Small delay between batches to avoid overwhelming the API
      if (i + batchSize < ifscCodes.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    return results
  }
}

// Export singleton instance
export const mainIFSCService = new MainIFSCService()
export default mainIFSCService
